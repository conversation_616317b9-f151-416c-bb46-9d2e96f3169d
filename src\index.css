@tailwind base;
@tailwind components;
@tailwind utilities;

/* Apple-inspired design system - Clean, minimal, premium
All colors MUST be HSL.
*/

@layer base {
  :root {
    /* Rainbow button colors */
    --color-1: 0 100% 63%;
    --color-2: 270 100% 63%;
    --color-3: 210 100% 63%;
    --color-4: 195 100% 63%;
    --color-5: 90 100% 63%;

    /* Pure backgrounds */
    --background: 0 0% 100%;
    --foreground: 0 0% 6%;

    /* Card and surface colors */
    --card: 0 0% 100%;
    --card-foreground: 0 0% 6%;

    /* Popover colors */
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 6%;

    /* Primary - Deep charcoal */
    --primary: 0 0% 6%;
    --primary-foreground: 0 0% 98%;

    /* Secondary - Light gray surface */
    --secondary: 0 0% 97%;
    --secondary-foreground: 0 0% 6%;

    /* Muted - Subtle gray backgrounds */
    --muted: 0 0% 96%;
    --muted-foreground: 0 0% 45%;

    /* Accent - Subtle blue for CTAs */
    --accent: 211 100% 50%;
    --accent-foreground: 0 0% 98%;

    /* Status colors */
    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 98%;

    /* Borders and inputs - Very subtle */
    --border: 0 0% 90%;
    --input: 0 0% 94%;
    --ring: 211 100% 50%;

    /* Radius - Apple-like subtle curves */
    --radius: 0.75rem;

    /* Custom Apple-inspired tokens */
    --text-primary: 0 0% 6%;
    --text-secondary: 0 0% 25%;
    --text-tertiary: 0 0% 45%;
    --surface-elevated: 0 0% 98%;
    --surface-subtle: 0 0% 96%;

    /* Animations - Subtle and refined */
    --transition-smooth: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
    --transition-quick: all 0.15s cubic-bezier(0.16, 1, 0.3, 1);

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;

    /* For glowing effect component */
    --black: 0 0% 0%;
  }

  .dark {
    /* Pure black backgrounds for dramatic effect */
    --background: 0 0% 0%;
    --foreground: 0 0% 100%;

    /* Card and surface colors - pure black with subtle elevation */
    --card: 0 0% 2%;
    --card-foreground: 0 0% 100%;

    /* Popover colors */
    --popover: 0 0% 2%;
    --popover-foreground: 0 0% 100%;

    /* Primary - pure white for main text */
    --primary: 0 0% 100%;
    --primary-foreground: 0 0% 0%;

    /* Secondary - very dark surface for contrast */
    --secondary: 0 0% 4%;
    --secondary-foreground: 0 0% 100%;

    /* Muted - subtle dark backgrounds */
    --muted: 0 0% 6%;
    --muted-foreground: 0 0% 70%;

    /* Accent - bright blue for CTAs in dark mode */
    --accent: 211 100% 60%;
    --accent-foreground: 0 0% 0%;

    /* Status colors */
    --destructive: 0 75% 55%;
    --destructive-foreground: 0 0% 100%;

    /* Borders and inputs - very subtle on black */
    --border: 0 0% 10%;
    --input: 0 0% 6%;
    --ring: 211 100% 60%;

    /* Custom dark mode tokens */
    --text-primary: 0 0% 100%;
    --text-secondary: 0 0% 85%;
    --text-tertiary: 0 0% 70%;
    --surface-elevated: 0 0% 2%;
    --surface-subtle: 0 0% 4%;

    /* Sidebar colors for dark mode */
    --sidebar-background: 0 0% 0%;
    --sidebar-foreground: 0 0% 100%;
    --sidebar-primary: 211 100% 60%;
    --sidebar-primary-foreground: 0 0% 0%;
    --sidebar-accent: 0 0% 4%;
    --sidebar-accent-foreground: 0 0% 100%;
    --sidebar-border: 0 0% 10%;
    --sidebar-ring: 211 100% 60%;

    /* For glowing effect component */
    --black: 0 0% 0%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-family:
      -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue",
      Arial, sans-serif;
    letter-spacing: -0.01em;
    line-height: 1.6;
  }

  /* Apple-inspired typography */
  .text-display {
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 700;
    line-height: 1.1;
    letter-spacing: -0.02em;
  }

  .text-heading {
    font-size: clamp(1.75rem, 3vw, 2.5rem);
    font-weight: 600;
    line-height: 1.2;
    letter-spacing: -0.015em;
  }

  .text-subheading {
    font-size: clamp(1.25rem, 2vw, 1.5rem);
    font-weight: 500;
    line-height: 1.3;
    letter-spacing: -0.01em;
  }

  .text-body {
    font-size: 1.125rem;
    font-weight: 400;
    line-height: 1.6;
    color: hsl(var(--text-secondary));
  }

  .text-caption {
    font-size: 0.875rem;
    font-weight: 400;
    line-height: 1.4;
    color: hsl(var(--text-tertiary));
  }
}

@layer components {
  /* Floating Navigation - Apple-inspired glass morphism */
  .floating-nav {
    @apply bg-background/70 backdrop-blur-xl border border-border/50;
    @apply rounded-2xl shadow-lg shadow-black/5;
    @apply transition-all duration-300 ease-out;
    @apply hover:shadow-xl hover:shadow-black/10;
    @apply dark:bg-background/80 dark:border-border/30;
    @apply dark:shadow-black/20 dark:hover:shadow-black/30;

    /* Enhanced glass effect */
    backdrop-filter: blur(20px) saturate(180%);
    -webkit-backdrop-filter: blur(20px) saturate(180%);

    /* Subtle inner glow */
    box-shadow:
      0 8px 32px rgba(0, 0, 0, 0.08),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
  }

  .dark .floating-nav {
    box-shadow:
      0 8px 32px rgba(0, 0, 0, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.05);
  }

  /* Navigation links with Apple-style hover effects */
  .nav-link {
    @apply px-3 py-2 rounded-lg text-sm font-medium;
    @apply text-foreground/80 hover:text-foreground;
    @apply hover:bg-secondary/50 transition-all duration-200;
    @apply relative overflow-hidden;
  }

  .nav-link::before {
    content: "";
    @apply absolute inset-0 bg-gradient-to-r from-accent/10 to-accent/5;
    @apply opacity-0 transition-opacity duration-200;
    @apply rounded-lg;
  }

  .nav-link:hover::before {
    @apply opacity-100;
  }

  /* Theme toggle button */
  .nav-theme-toggle {
    @apply w-9 h-9 rounded-lg;
    @apply hover:bg-secondary/50 transition-all duration-200;
    @apply relative overflow-hidden;
  }

  /* CTA button in nav */
  .nav-cta {
    @apply bg-accent text-accent-foreground;
    @apply px-4 py-2 rounded-lg font-medium;
    @apply hover:bg-accent/90 transition-all duration-200;
    @apply hover:scale-105 hover:shadow-lg;
    @apply text-sm;
  }

  /* Glass dropdown */
  .glass-dropdown {
    @apply bg-background/90 backdrop-blur-xl border border-border/50;
    @apply rounded-xl shadow-xl;
    backdrop-filter: blur(20px) saturate(180%);
    -webkit-backdrop-filter: blur(20px) saturate(180%);
  }

  /* Mobile menu animations */
  @media (max-width: 768px) {
    .floating-nav {
      @apply mx-2;
      animation: slideDown 0.3s cubic-bezier(0.16, 1, 0.3, 1);
    }
  }

  @keyframes slideDown {
    from {
      opacity: 0;
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Enhanced hover effects for better interaction feedback */
  .floating-nav:hover {
    transform: translateY(-1px);
  }

  /* Smooth scrolling for anchor links */
  html {
    scroll-behavior: smooth;
  }

  /* Apple-inspired animations */
  .fade-in {
    animation: fadeIn 0.6s cubic-bezier(0.16, 1, 0.3, 1) forwards;
    opacity: 0;
    transform: translateY(20px);
  }

  .fade-in-delay-1 {
    animation-delay: 0.1s;
  }

  .fade-in-delay-2 {
    animation-delay: 0.2s;
  }

  .fade-in-delay-3 {
    animation-delay: 0.3s;
  }

  @keyframes fadeIn {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Apple-inspired button variants */
  .btn-primary {
    @apply bg-accent text-accent-foreground px-8 py-3 rounded-xl font-medium transition-all duration-200;
    @apply hover:bg-accent/90 hover:shadow-lg hover:scale-[1.02];
  }

  .btn-secondary {
    @apply bg-secondary text-secondary-foreground px-8 py-3 rounded-xl font-medium transition-all duration-200;
    @apply hover:bg-secondary/80 hover:shadow-md hover:scale-[1.02];
  }

  .btn-ghost {
    @apply text-primary px-6 py-2 rounded-lg font-medium transition-all duration-200;
    @apply hover:bg-secondary/50;
  }

  /* Enhanced pricing card effects */
  .pricing-card {
    @apply transition-all duration-300 ease-out;
    @apply hover:shadow-2xl hover:-translate-y-2;
    @apply relative overflow-hidden;
  }

  .pricing-card::before {
    content: "";
    @apply absolute inset-0 bg-gradient-to-br from-accent/5 to-transparent;
    @apply opacity-0 transition-opacity duration-300;
  }

  .pricing-card:hover::before {
    @apply opacity-100;
  }

  /* Popular plan glow effect */
  .pricing-popular {
    @apply shadow-xl shadow-accent/20;
    animation: pulse-glow 3s ease-in-out infinite;
  }

  @keyframes pulse-glow {
    0%,
    100% {
      box-shadow: 0 0 20px rgba(59, 130, 246, 0.15);
    }
    50% {
      box-shadow: 0 0 30px rgba(59, 130, 246, 0.25);
    }
  }

  /* Advanced pricing component animations */
  .pricing-3d-effect {
    perspective: 1000px;
    transform-style: preserve-3d;
  }

  .pricing-card-3d {
    transition: transform 0.6s cubic-bezier(0.16, 1, 0.3, 1);
  }

  .pricing-card-3d:hover {
    transform: rotateY(5deg) translateZ(20px);
  }

  /* Number flow animations */
  .number-flow-container {
    @apply font-mono tabular-nums;
  }

  /* Star border component enhancements */
  .star-border-button {
    @apply relative overflow-hidden;
    @apply transition-all duration-300 ease-out;
    @apply hover:scale-105 hover:shadow-xl;
  }

  .star-border-button::before {
    content: "";
    @apply absolute inset-0 bg-gradient-to-r from-accent/10 via-accent/5 to-accent/10;
    @apply opacity-0 transition-opacity duration-300;
    @apply rounded-lg;
  }

  .star-border-button:hover::before {
    @apply opacity-100;
  }

  /* Enhanced star animations */
  @keyframes star-twinkle {
    0%,
    100% {
      opacity: 0;
      transform: scale(0) rotate(0deg);
    }
    50% {
      opacity: 1;
      transform: scale(1) rotate(180deg);
    }
  }

  .star-twinkle {
    animation: star-twinkle 2s ease-in-out infinite;
  }

  /* Section spacing */
  .section-padding {
    @apply py-20 lg:py-32;
  }

  .container-padding {
    @apply px-6 sm:px-8 lg:px-12 xl:px-16 max-w-7xl mx-auto;
  }

  /* Calendar booking styles */
  .no-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }

  /* Ballpit cursor styles */
  .cursor-none {
    cursor: none !important;
  }

  .cursor-none * {
    cursor: none !important;
  }

  /* Override cursor for interactive elements in ballpit sections */
  .cursor-pointer {
    cursor: pointer !important;
  }
}
